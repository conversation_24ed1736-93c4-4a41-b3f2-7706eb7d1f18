import { useState, useEffect } from 'react';
import { useFetchQuery } from '@/hooks/repository/useFetchQuery';
import { useUser } from '@/context/UserContext';

export interface UserRoleData {
  userID: string;
  role: 'user' | 'moderator' | 'admin';
  permissions: string[];
}

export function useUserRole() {
  const { user } = useUser();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isModerator, setIsModerator] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<'user' | 'moderator' | 'admin'>('user');

  useEffect(() => {
    const fetchUserRole = async () => {
      if (user?.userID) {
        try {
          // Essayons de récupérer le profil utilisateur qui contient peut-être le rôle
          const token = await import("@/hooks/useSetToken").then(m => m.getToken());
          const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/profile/${user.userID}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();
            console.log('📋 Profile data:', data);

            // Si le profil contient le rôle, l'utiliser
            const role = data.data?.role || 'user';
            setUserRole(role);
            setIsAdmin(role === 'admin');
            setIsModerator(role === 'moderator' || role === 'admin');

            console.log('🔑 User Role from API:', {
              userID: user.userID,
              role: role,
              isAdmin: role === 'admin',
              isModerator: role === 'moderator' || role === 'admin'
            });
          } else {
            console.log('❌ Failed to fetch profile, using default role');
            // Fallback : rôle par défaut
            setUserRole('user');
            setIsAdmin(false);
            setIsModerator(false);
          }
        } catch (error) {
          console.error('❌ Error fetching user role:', error);
          // En cas d'erreur, rôle par défaut
          setUserRole('user');
          setIsAdmin(false);
          setIsModerator(false);
        }
      }
      setIsLoading(false);
    };

    fetchUserRole();
  }, [user?.userID]);

  return {
    isAdmin,
    isModerator,
    isLoading,
    role: userRole,
    permissions: isAdmin ? ['moderate', 'admin'] : isModerator ? ['moderate'] : [],
    error: null
  };
}

// Hook pour vérifier si l'utilisateur a une permission spécifique
export function useHasPermission(permission: string) {
  const { permissions, isLoading } = useUserRole();
  
  return {
    hasPermission: permissions.includes(permission),
    isLoading
  };
}

// Hook pour vérifier si l'utilisateur peut modérer
export function useCanModerate() {
  const { isModerator, isLoading } = useUserRole();
  
  return {
    canModerate: isModerator,
    isLoading
  };
}

// Hook pour vérifier si l'utilisateur peut accéder aux fonctionnalités admin
export function useCanAccessAdmin() {
  const { isAdmin, isLoading } = useUserRole();
  
  return {
    canAccess: isAdmin,
    isLoading
  };
}
